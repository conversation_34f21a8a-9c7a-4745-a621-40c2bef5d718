# ============================
#  DATA Extraction CONFIG
# ============================
# Atlassian confluence credentials
CONFLUENCE_BASE_URL=https://***.atlassian.net/wiki
SPACE_KEY=***
USER_EMAIL=***@***.com
API_TOKEN=*****

# ============================
#  DATA CLEANING CONFIG
# ============================

# Use either Ollama (local) or OpenRouter (cloud). 
# Uncomment the desired provider and ensure only one is active.

# ------ Ollama (Local) ------
BASE_URL=http://localhost:11434/v1
MODEL=llama3.2
API_KEY=ollama

# ------ OpenRouter (Cloud) ------
# BASE_URL=https://openrouter.ai/api/v1
# MODEL=google/gemini-2.0-flash-thinking-exp-1219:free
# MODEL=google/gemini-2.0-flash-lite-preview-02-05:free
# MODEL=google/gemini-2.0-pro-exp-02-05:free
# MODEL=mistralai/mistral-7b-instruct:free
# MODEL=qwen/qwen2.5-vl-72b-instruct:free
# MODEL=sophosympatheia/rogue-rose-103b-v0.2:free
# MODEL=cognitivecomputations/dolphin3.0-r1-mistral-24b:free
# API_KEY=*****  # Replace with OpenRouter API key
# -----------------------------------------------


# Embedding model (used for document embedding)
EMBEDDING_MODEL_STANDARD=BAAI/bge-large-en-v1.5
EMBEDDING_MODEL_EXPERT=BAAI/bge-large-en-v1.5
EMBEdding_MODEL_LITE=sentence-transformers/all-MiniLM-L6-v2

# Set the application mode: 
# - LITE: Basic features with minimal complexity  
# - STANDARD: Balanced mode with essential features  
# - EXPERT: Full-featured mode with advanced capabilities  
APP_MODE=LITE
