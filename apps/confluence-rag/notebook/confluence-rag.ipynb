{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Load and chunk PDFs"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Script Directory: /Users/<USER>/codebase/ritesh-codebase/workspaceGPT/apps/confluence-rag\n"]}], "source": ["from langchain_text_splitters import RecursiveCharacterTextSplitter\n", "from langchain_community.document_loaders import PyMuPDFLoader\n", "import os\n", "from pathlib import Path\n", "from langchain_community.vectorstores import Chroma\n", "from langchain_community.embeddings import HuggingFaceEmbeddings\n", "from langchain_ollama import ChatOllama\n", "from langchain.chains import ConversationalRetrievalChain\n", "from langchain.memory import ConversationBufferMemory\n", "from langchain.callbacks.base import BaseCallbackHandler\n", "import gradio as gr\n", "\n", "# Constants\n", "MODEL = \"llama3.2\"\n", "DB_NAME = \"vector_db\"\n", "OLLAMA_API = \"http://localhost:11434/api/chat\"\n", "\n", "# script_dir = Path(__file__).parent\n", "script_dir = os.getcwd()\n", "print(\"Script Directory:\", script_dir)\n", "\n", "# Function to load PDFs\n", "def load_pdfs(pdf_folder):\n", "    documents = []\n", "    for file in os.listdir(pdf_folder):\n", "        if file.endswith(\".pdf\"):\n", "            loader = PyMuPDFLoader(os.path.join(pdf_folder, file))\n", "            documents.extend(loader.load())\n", "    return documents\n", "\n", "# Function to chunk documents\n", "def chunk_documents(documents):\n", "    text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=200)\n", "    return text_splitter.split_documents(documents)\n", "\n", "# Load and process documents\n", "# pdf_folder = Path(__file__).parent\n", "pdf_folder = os.getcwd()\n", "documents = load_pdfs(pdf_folder)\n", "if not documents:\n", "    print(\"No documents found\")\n", "    exit()\n", "chunks = chunk_documents(documents)\n", "\n", "# Convert text into embeddings and store in vector DB\n", "embeddings = HuggingFaceEmbeddings(model_name=\"sentence-transformers/all-MiniLM-L6-v2\")\n", "vectorstore = Chroma.from_documents(chunks, embeddings, persist_directory=\"./chroma_db\")\n", "\n", "# Create retriever\n", "retriever = vectorstore.as_retriever(search_kwargs={\"k\": 3})\n", "\n", "# Create llm\n", "llm = ChatOllama(model=MODEL)\n", "\n", "# Initialize memory\n", "memory = ConversationBufferMemory(memory_key=\"chat_history\", return_messages=True)\n", "\n", "# Create conversational chain\n", "qa_chain = ConversationalRetrievalChain.from_llm(llm=llm, retriever=retriever, memory=memory)\n", "\n"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Document(metadata={'author': '', 'creationDate': \"D:20250217171142+00'00'\", 'creationdate': '2025-02-17T17:11:42+00:00', 'creator': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/133.0.0.0 Safari/537.36', 'file_path': '/Users/<USER>/codebase/ritesh-codebase/workspaceGPT/apps/confluence-rag/B Test for Product Price Experimentation - How to Setup and Test-170225-171133.pdf', 'format': 'PDF 1.4', 'keywords': '', 'modDate': \"D:20250217171142+00'00'\", 'moddate': '2025-02-17T17:11:42+00:00', 'page': 1, 'producer': 'Skia/PDF m133', 'source': '/Users/<USER>/codebase/ritesh-codebase/workspaceGPT/apps/confluence-rag/B Test for Product Price Experimentation - How to Setup and Test-170225-171133.pdf', 'subject': '', 'title': 'A/B Test for Product Price Experimentation - How to Setup and Test - D2C - Confluence', 'total_pages': 2, 'trapped': ''}, page_content='Test\\nTesting Organically\\n1. Visit the mms storefront you wish to test on\\n2. You will be given a unique optimizely_user_id\\n3. That user will either be On (part of the experiment) or Off (not part of the experiment)\\n4. The rules to determine how those decisions are distributed amongst users are configured in Optimizely\\nForcing an Optimizely Decision\\n1. You can force your user to be either on  or off  via the optimizely_user_id\\n2. Go to Chrome > Dev Tools > Application Tab\\n3. Under Cookies, select optimizely_user_id . Double click the value column and enter on  or off .'),\n", " Document(metadata={'author': '', 'creationDate': \"D:20250217171142+00'00'\", 'creationdate': '2025-02-17T17:11:42+00:00', 'creator': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/133.0.0.0 Safari/537.36', 'file_path': '/Users/<USER>/codebase/ritesh-codebase/workspaceGPT/apps/confluence-rag/B Test for Product Price Experimentation - How to Setup and Test-170225-171133.pdf', 'format': 'PDF 1.4', 'keywords': '', 'modDate': \"D:20250217171142+00'00'\", 'moddate': '2025-02-17T17:11:42+00:00', 'page': 1, 'producer': 'Skia/PDF m133', 'source': '/Users/<USER>/codebase/ritesh-codebase/workspaceGPT/apps/confluence-rag/B Test for Product Price Experimentation - How to Setup and Test-170225-171133.pdf', 'subject': '', 'title': 'A/B Test for Product Price Experimentation - How to Setup and Test - D2C - Confluence', 'total_pages': 2, 'trapped': ''}, page_content='Test\\nTesting Organically\\n1. Visit the mms storefront you wish to test on\\n2. You will be given a unique optimizely_user_id\\n3. That user will either be On (part of the experiment) or Off (not part of the experiment)\\n4. The rules to determine how those decisions are distributed amongst users are configured in Optimizely\\nForcing an Optimizely Decision\\n1. You can force your user to be either on  or off  via the optimizely_user_id\\n2. Go to Chrome > Dev Tools > Application Tab\\n3. Under Cookies, select optimizely_user_id . Double click the value column and enter on  or off .'),\n", " Document(metadata={'author': '', 'creationDate': \"D:20250217171142+00'00'\", 'creationdate': '2025-02-17T17:11:42+00:00', 'creator': 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) HeadlessChrome/133.0.0.0 Safari/537.36', 'file_path': '/Users/<USER>/codebase/ritesh-codebase/workspaceGPT/apps/confluence-rag/B Test for Product Price Experimentation - How to Setup and Test-170225-171133.pdf', 'format': 'PDF 1.4', 'keywords': '', 'modDate': \"D:20250217171142+00'00'\", 'moddate': '2025-02-17T17:11:42+00:00', 'page': 1, 'producer': 'Skia/PDF m133', 'source': '/Users/<USER>/codebase/ritesh-codebase/workspaceGPT/apps/confluence-rag/B Test for Product Price Experimentation - How to Setup and Test-170225-171133.pdf', 'subject': '', 'title': 'A/B Test for Product Price Experimentation - How to Setup and Test - D2C - Confluence', 'total_pages': 2, 'trapped': ''}, page_content='Test\\nTesting Organically\\n1. Visit the mms storefront you wish to test on\\n2. You will be given a unique optimizely_user_id\\n3. That user will either be On (part of the experiment) or Off (not part of the experiment)\\n4. The rules to determine how those decisions are distributed amongst users are configured in Optimizely\\nForcing an Optimizely Decision\\n1. You can force your user to be either on  or off  via the optimizely_user_id\\n2. Go to Chrome > Dev Tools > Application Tab\\n3. Under Cookies, select optimizely_user_id . Double click the value column and enter on  or off .')]"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["query=\"hi\"\n", "retrieved_docs = retriever.invoke(query)\n", "\n", "for idx, doc in enumerate(retrieved_docs):\n", "    title = doc.metadata.get(\"title\", \"No Title\")\n", "    content = doc.page_content  # Extracts the document's text content\n", "    \n", "    # print(f\"Document {idx + 1}:\")\n", "    print(f\"Title: {title}\")\n", "    print(f\"Content:\\n{content}\\n\")\n", "    print(\"=\" * 80)  # Separator for readability"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["* Running on local URL:  http://127.0.0.1:7862\n", "\n", "To create a public link, set `share=True` in `launch()`.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["huggingface/tokenizers: The current process just got forked, after parallelism has already been used. Disabling parallelism to avoid deadlocks...\n", "To disable this warning, you can either:\n", "\t- Avoid using `tokenizers` before the fork if possible\n", "\t- Explicitly set the environment variable TOKENIZERS_PARALLELISM=(true | false)\n"]}, {"data": {"text/html": ["<div><iframe src=\"http://127.0.0.1:7862/\" width=\"100%\" height=\"500\" allow=\"autoplay; camera; microphone; clipboard-read; clipboard-write;\" frameborder=\"0\" allowfullscreen></iframe></div>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def chat(question, history):\n", "    if question.lower() in [\"hi\", \"hello\"]:\n", "        return \"Hi, I am Workspace Assistant. How can I help you?\"\n", "\n", "    result = qa_chain.invoke({\"question\": question})\n", "    return result[\"answer\"]\n", "\n", "# And in Gradio:\n", "\n", "view = gr.ChatInterface(chat, type=\"messages\").launch(inbrowser=True)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["\n", "# Custom Streamlit Callback\n", "class StreamHandler(BaseCallbackHandler):\n", "    def __init__(self, container):\n", "        self.container = container\n", "        self.text = \"\"\n", "\n", "    def on_llm_new_token(self, token: str, **kwargs):\n", "        self.text += token\n", "        self.container.markdown(self.text)  # Update Streamlit container dynamically\n", "\n", "# Streamlit UI\n", "\n", "st.title(\"Chat with FAISS-powered RAG\")\n", "query = st.text_input(\"Ask a question:\")\n", "\n", "if query:\n", "    with st.container():\n", "        # Initialize Streamlit callback\n", "        stream_handler = StreamHandler(st.empty())\n", "\n", "        # Initialize LLM with streaming handler\n", "        llm = ChatOllama(model=MODEL, callbacks=[stream_handler])\n", "\n", "        # Initialize memory\n", "        memory = ConversationBufferMemory(memory_key=\"chat_history\", return_messages=True)\n", "\n", "        # Create conversational chain\n", "        qa_chain = ConversationalRetrievalChain.from_llm(llm=llm, retriever=retriever, memory=memory)\n", "\n", "        # Invoke chain\n", "        qa_chain.invoke({\"question\": query})"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Convert text into embeddings and store in vectorDB"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["from langchain_community.vectorstores import Chroma\n", "from langchain_community.embeddings import HuggingFaceEmbeddings"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/var/folders/t5/12fvt5jj3_s7f2ht9mp6qv840000gp/T/ipykernel_2440/304350042.py:1: LangChainDeprecationWarning: The class `HuggingFaceEmbeddings` was deprecated in LangChain 0.2.2 and will be removed in 1.0. An updated version of the class exists in the :class:`~langchain-huggingface package and should be used instead. To use it run `pip install -U :class:`~langchain-huggingface` and import as `from :class:`~langchain_huggingface import HuggingFaceEmbeddings``.\n", "  embeddings = HuggingFaceEmbeddings(model_name=\"sentence-transformers/all-MiniLM-L6-v2\")\n"]}], "source": ["embeddings = HuggingFaceEmbeddings(model_name=\"sentence-transformers/all-MiniLM-L6-v2\")\n", "\n", "# embeddings = HuggingFaceEmbeddings(model_name=\"sentence-transformers/all-MiniLM-L6-v2\", model_kwargs={\"device\": \"cuda\"})\n", "\n", "vectorstore = Chroma.from_documents(chunks, embeddings, persist_directory=\"./chroma_db\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Query the vecor database"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Number of requested results 5 is greater than number of elements in index 3, updating n_results = 3\n"]}], "source": ["retriever = vectorstore.as_retriever(search_kwargs={\"k\":5})\n", "query=\"what is price experimentation\"\n", "retrieved_docs = retriever.invoke(query)\n", "\n", "# for doc in retrieved_docs:\n", "#     print(doc.page_content)"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [{"data": {"text/plain": ["384"]}, "execution_count": 56, "metadata": {}, "output_type": "execute_result"}], "source": ["# Find how many dimensions vector has\n", "\n", "collection = vectorstore._collection\n", "sample_embedding = collection.get(limit=1, include=[\"embeddings\"])\n", "len(sample_embedding[\"embeddings\"][0])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Generate response with ollama with memory"]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Number of requested results 5 is greater than number of elements in index 3, updating n_results = 3\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Price experimentation, also known as A/B testing for product pricing, is a method used to determine whether changing the price of a product or service results in an increase or decrease in sales, revenue, or other desired metrics.\n", "\n", "The goal of price experimentation is to identify the optimal price point for a product or service that maximizes revenue while still being competitive and appealing to customers. This can be particularly challenging when trying to balance profitability with customer demand and market conditions.\n", "\n", "Price experimentation involves comparing two or more different pricing scenarios:\n", "\n", "1. **Control group**: The current, existing price of the product or service.\n", "2. **Treatment group**: A new, experimental price that is being tested.\n", "3. **Target audience**: The specific customers who will be exposed to each pricing scenario.\n", "\n", "By analyzing the performance of both groups over a statistically significant period, you can determine which price performs better and make data-driven decisions about future pricing strategies.\n", "\n", "Some key benefits of price experimentation include:\n", "\n", "1. **Increased revenue**: By identifying the optimal price point, you can maximize revenue without sacrificing customer demand.\n", "2. **Competitive advantage**: Understanding your customers' willingness to pay helps you stay competitive in the market.\n", "3. **Improved profitability**: Price experimentation can help you balance pricing with profitability goals.\n", "\n", "In the context of the provided text, price experimentation is being conducted using Optimizely, a popular A/B testing and personalization platform, to determine the effectiveness of different product prices on sales and revenue."]}], "source": ["from langchain_ollama import ChatOllama\n", "from langchain.chains import ConversationalRetrievalChain\n", "from langchain.memory import ConversationBufferMemory\n", "from langchain.callbacks import StreamingStdOutCallbackHandler\n", "\n", "llm = ChatOllama(model=MODEL, callbacks = [StreamingStdOutCallbackHandler()])\n", "\n", "#initialize memory\n", "memory = ConversationBufferMemory(memory_key=\"chat_history\", return_messages=True)\n", "\n", "#create QA chain\n", "qa_chain = ConversationalRetrievalChain.from_llm(llm=llm, retriever=retriever,memory=memory)\n", "result = qa_chain.invoke({\"question\": \"what is price experimentaion\"})\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Gradio interface"]}, {"cell_type": "code", "execution_count": 61, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 62, "metadata": {}, "outputs": [], "source": ["def chat_generator(query):\n", "    \"\"\"Generator function to stream chat output.\"\"\"\n", "    for chunk in qa_chain.stream({\"question\": query}):\n", "        if \"answer\" in chunk:\n", "            yield chunk[\"answer\"]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["huggingface/tokenizers: The current process just got forked, after parallelism has already been used. Disabling parallelism to avoid deadlocks...\n", "To disable this warning, you can either:\n", "\t- Avoid using `tokenizers` before the fork if possible\n", "\t- Explicitly set the environment variable TOKENIZERS_PARALLELISM=(true | false)\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "      👋 \u001b[1mWelcome to Streamlit!\u001b[0m\n", "\n", "      If you’d like to receive helpful onboarding emails, news, offers, promotions,\n", "      and the occasional swag, please enter your email address below. Otherwise,\n", "      leave this field blank.\n", "\n", "      \u001b[34mEmail: \u001b[0m "]}], "source": ["! streamlit run ./chat_interface.py"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["hi"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.11"}}, "nbformat": 4, "nbformat_minor": 4}