{"name": "confluence-rag", "version": "1.0.0", "main": "index.js", "scripts": {"start": "streamlit run src/chat.py", "reset": "shx rm -rf vector_db", "#postinstall": "pnpm run env:create", "env:create": "conda info --envs | grep -w workspacegpt || conda env create -f environment.yml", "env:update": "conda env update -f environment.yml --prune", "env:remove": "conda env remove --name workspacegpt", "env:info": "echo 'To activate the environment, run: conda activate workspacegpt'"}, "author": "<PERSON><PERSON> kant", "license": "ISC", "description": "", "dependencies": {"shx": "^0.3.4"}}