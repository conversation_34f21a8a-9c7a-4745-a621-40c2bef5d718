<svg width="500" height="400" viewBox="0 0 500 400" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Background with gradient -->
  <rect width="500" height="400" rx="10" fill="url(#paint0_linear)" />
  
  <!-- VSCode window frame -->
  <rect x="50" y="50" width="400" height="300" rx="8" fill="#1E1E1E" />
  
  <!-- Window title bar -->
  <rect x="50" y="50" width="400" height="30" rx="8 8 0 0" fill="#333333" />
  
  <!-- Window controls -->
  <circle cx="70" cy="65" r="5" fill="#FF5F56" />
  <circle cx="90" cy="65" r="5" fill="#FFBD2E" />
  <circle cx="110" cy="65" r="5" fill="#27C93F" />
  
  <!-- Sidebar -->
  <rect x="50" y="80" width="80" height="270" fill="#252526" />
  
  <!-- Sidebar icons -->
  <rect x="65" y="100" width="50" height="5" rx="2.5" fill="#6A737D" />
  <rect x="65" y="120" width="50" height="5" rx="2.5" fill="#6A737D" />
  <rect x="65" y="140" width="50" height="5" rx="2.5" fill="#6A737D" />
  <rect x="65" y="160" width="50" height="5" rx="2.5" fill="#6A737D" />
  
  <!-- Main content area -->
  <rect x="130" y="80" width="320" height="270" fill="#1E1E1E" />
  
  <!-- Code lines -->
  <rect x="150" y="100" width="280" height="5" rx="2.5" fill="#6A737D" opacity="0.7" />
  <rect x="150" y="115" width="200" height="5" rx="2.5" fill="#6A737D" opacity="0.7" />
  <rect x="150" y="130" width="250" height="5" rx="2.5" fill="#6A737D" opacity="0.7" />
  <rect x="150" y="145" width="180" height="5" rx="2.5" fill="#6A737D" opacity="0.7" />
  <rect x="150" y="160" width="220" height="5" rx="2.5" fill="#6A737D" opacity="0.7" />
  
  <!-- Chat interface -->
  <rect x="150" y="190" width="280" height="100" rx="5" fill="#2D2D2D" />
  <rect x="160" y="200" width="260" height="40" rx="3" fill="#3E3E3E" />
  <rect x="160" y="250" width="260" height="30" rx="15" fill="#4A4A4A" />
  
  <!-- WorkspaceGPT logo/text -->
  <text x="250" y="320" font-family="Arial" font-size="16" fill="white" text-anchor="middle">WorkspaceGPT</text>
  
  <!-- Gradient definition -->
  <defs>
    <linearGradient id="paint0_linear" x1="0" y1="0" x2="500" y2="400" gradientUnits="userSpaceOnUse">
      <stop stop-color="#4F46E5" />
      <stop offset="1" stop-color="#3B82F6" />
    </linearGradient>
  </defs>
</svg>