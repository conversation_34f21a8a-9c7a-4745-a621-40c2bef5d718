{"name": "confluence-extractor", "version": "1.0.0", "description": "", "main": "dist/index.js", "type": "module", "scripts": {"start": "node dist/index.js", "clean": "rm -rf dist", "build": "node esbuild.config.js", "dev": "node esbuild.config.js --watch", "watch": "node esbuild.config.js --watch"}, "keywords": [], "author": "<PERSON><PERSON> kant", "license": "ISC", "dependencies": {"@workspace-gpt/confluence-utils": "workspace:*", "dotenv": "^16.4.7", "openai": "^4.85.4", "pdf-parse": "^1.1.1", "prettier": "^3.5.0"}, "devDependencies": {"@types/node": "^20.0.0", "esbuild": "^0.20.1", "ts-node": "^10.9.2", "typescript": "^5.0.0"}}