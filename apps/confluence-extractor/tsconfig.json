{"compilerOptions": {"target": "ES2020", "module": "NodeNext", "moduleResolution": "NodeNext", "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "allowJs": true, "resolveJsonModule": true, "composite": true, "declaration": true, "declarationMap": true, "sourceMap": true, "incremental": true}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}