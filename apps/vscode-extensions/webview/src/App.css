/* Main container styles */
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  padding: 0;
  box-sizing: border-box;
  font-size: 0.8rem;
  /* background-color: var(--vscode-editor-background); */
  color: var(--vscode-editor-foreground);
  position: relative;
  padding-top: var(--banner-height, 0);
}

.banner-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  padding: 4px 8px;
  background-color: var(--vscode-inputValidation-warningBackground);
  border-bottom: 1px solid var(--vscode-inputValidation-warningBorder);
  backdrop-filter: blur(8px);
  transform: translateY(0);
  transition: transform 0.3s ease;
  height: var(--banner-height, 30px);
}

.chat-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  /* padding: 0.5rem 0.75rem; */
  border-bottom: 1px solid var(--vscode-editor-lineHighlightBorder);
  width: 100%;
  box-sizing: border-box;
}

.chat-header h2 {
  margin: 0;
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--vscode-foreground);
}

.header-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  align-items: center;
}

.new-chat-button,
.settings-button {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--vscode-icon-foreground);
  background: none;
  border: none;
  cursor: pointer;
  width: 32px;
  height: 32px;
  transition: all 0.2s ease;
  border-radius: 4px;
}

.new-chat-button::before {
  content: '+';
  display: block;
  width: 16px;
  height: 16px;
  font-size: 20px;
  line-height: 16px;
  text-align: center;
}

.settings-button::before {
  content: url("data:image/svg+xml,%3Csvg width='16' height='16' viewBox='0 0 16 16' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill='currentColor' d='M9.1 4.4L8.6 2H7.4l-.5 2.4-.7.3-2-1.3-.9.8 1.3 2-.2.7-2.4.5v1.2l2.4.5.3.8-1.3 2 .8.8 2-1.3.8.3.4 2.3h1.2l.5-2.4.8-.3 2 1.3.8-.8-1.3-2 .3-.8 2.3-.4V7.4l-2.4-.5-.3-.8 1.3-2-.8-.8-2 1.3-.7-.2zM8 9.5A1.5 1.5 0 1 1 8 6.5a1.5 1.5 0 0 1 0 3z'/%3E%3C/svg%3E");
  display: block;
  width: 16px;
  height: 16px;
}

.new-chat-button:hover,
.settings-button:hover {
  color: var(--vscode-foreground);
  background-color: var(--vscode-toolbar-hoverBackground);
}

/* Welcome container styles */
.welcome-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 800px;
  padding: 0.5rem;
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.welcome-title {
  font-size: 1.7rem;
  font-weight: 700;
  margin: 1rem;
  background: linear-gradient(
    135deg,
    var(--vscode-textLink-foreground),
    var(--vscode-textLink-activeForeground)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-align: center;
  letter-spacing: -0.02em;
}

.welcome-subtitle {
  font-size: 1rem;
  color: var(--vscode-descriptionForeground);
  margin: 0;
  text-align: center;
  font-weight: 500;
  letter-spacing: -0.01em;
}

.privacy-container {
  max-width: 600px;
  padding: 1rem;
  background: linear-gradient(
    145deg,
    var(--vscode-editor-background),
    color-mix(
      in srgb,
      var(--vscode-editor-background) 95%,
      var(--vscode-textLink-foreground)
    )
  );
  border: 1px solid var(--vscode-textLink-foreground);
  border-radius: 16px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
  transform: translateY(0);
  transition: all 0.3s ease;
  margin: 1rem auto;
}

.privacy-container:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 28px rgba(0, 0, 0, 0.15);
}

.privacy-message {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  font-size: 0.85rem;
  line-height: 1.5;
  color: var(--vscode-editor-foreground);
}

.privacy-icon {
  font-size: 1.7rem;
  color: var(--vscode-textLink-foreground);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.tips-container {
  margin-top: 0.5rem;
}

.tips-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: 1.2rem;
  color: var(--vscode-editor-foreground);
  text-align: center;
}

.tips-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  padding: 0.5rem;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.25rem;
  background: linear-gradient(
    145deg,
    var(--vscode-editor-background),
    color-mix(
      in srgb,
      var(--vscode-editor-background) 97%,
      var(--vscode-textLink-foreground)
    )
  );
  border: 1px solid var(--vscode-editor-lineHighlightBorder);
  border-radius: 12px;
  transition: all 0.2s ease;
}

.tip-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  border-color: var(--vscode-textLink-foreground);
}

.tip-icon {
  font-size: 1.3rem;
  color: var(--vscode-textLink-foreground);
}

.tips-title {
  font-size: 1.1rem;
  font-weight: 500;
  margin-bottom: 1rem;
  color: var(--vscode-editor-foreground);
}

.tips-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.tip-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 0.75rem;
  background-color: var(--vscode-editor-background);
  border: 1px solid var(--vscode-editor-lineHighlightBorder);
  border-radius: 8px;
}

.tip-icon {
  font-size: 1rem;
}

.tip-item code {
  background-color: var(--vscode-textBlockQuote-background);
  padding: 0.2em 0.4em;
  border-radius: 4px;
  font-family: var(--vscode-editor-font-family);
  font-size: 0.8em;
}

/* Messages container styles */
.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 2rem 0.75rem 0.75rem;
  display: flex;
  flex-direction: column;
  gap: 0.6rem;
}

/* Message styles */
.message {
  padding: 0.8rem;
  margin: 0.4rem 0;
  border-radius: 8px;
  max-width: 100%;
  word-wrap: break-word;
  font-size: 0.8rem;
}

.error-message {
  background-color: var(--vscode-inputValidation-errorBackground) !important;
  border: 1px solid var(--vscode-inputValidation-errorBorder) !important;
  color: var(--vscode-inputValidation-errorForeground) !important;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem;
  margin: 0.5rem 0;
  border-radius: 8px;
  animation: shake 0.5s ease-in-out;
  max-width: 100%;
}

.error-message::before {
  content: '⚠️';
  font-size: 1.2rem;
  margin-right: 0.5rem;
}

.error-message .message-content {
  color: var(--vscode-inputValidation-errorForeground) !important;
}

@keyframes shake {
  0%,
  100% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(-5px);
  }
  75% {
    transform: translateX(5px);
  }
}

.user-message {
  align-self: flex-end;
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
}

.assistant-message {
  align-self: flex-start;
  background-color: var(--vscode-editor-background);
  border: 1px solid var(--vscode-editor-lineHighlightBorder);
  color: var(--vscode-editor-foreground);
}

/* Input container styles */
.input-container {
  display: flex;
  flex-direction: column;
  padding: 0.75rem;
  margin: 0.25rem;
  border-radius: 1rem;
  border: 1px solid var(--vscode-chart-line);
  background-color: var(--vscode-input-background);
}

.input-wrapper {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;
}
.input-wrapper input {
  border: none;
}

.input-controls {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 0.5rem;
  width: 100%;
}

/* Input field styles */
input {
  flex: 1;
  padding: 0.5rem;
  border: none;
  background-color: var(--vscode-input-background);
  color: var(--vscode-input-foreground);
  border-radius: 4px;
}

input:focus {
  outline: none;
  border-color: var(--vscode-focusBorder);
}

/* Button styles */
button {
  padding: 0.5rem 1rem;
  background-color: var(--vscode-button-background);
  color: var(--vscode-button-foreground);
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.send-button {
  height: 24px;
  width: 24px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  background-color: transparent;
  color: var(--vscode-icon-foreground);
}

button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

button:hover:not(:disabled) {
  background-color: var(--vscode-button-hoverBackground);
}

/* Loading indicator styles */
.loading-indicator {
  padding: 0.8rem;
  text-align: center;
  color: var(--vscode-descriptionForeground);
  font-style: italic;
  font-size: 0.8rem;
}

.markdown-content {
  font-family: var(--vscode-font-family);
  line-height: 1.5;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin-top: 1em;
  margin-bottom: 0.5em;
  color: var(--vscode-foreground);
}

.markdown-content p {
  margin: 0.5em 0;
}

.markdown-content ul,
.markdown-content ol {
  margin: 0.5em 0;
  padding-left: 1.5em;
}

.markdown-content code {
  font-family: var(--vscode-editor-font-family);
  background-color: var(--vscode-textBlockQuote-background);
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-size: 0.8em;
}

.markdown-content pre {
  background-color: var(--vscode-textBlockQuote-background);
  padding: 1em;
  border-radius: 4px;
  overflow-x: auto;
}

.markdown-content blockquote {
  border-left: 4px solid var(--vscode-textBlockQuote-border);
  margin: 0.5em 0;
  padding-left: 1em;
  color: var(--vscode-textBlockQuote-foreground);
}

.markdown-content a {
  color: var(--vscode-textLink-foreground);
  text-decoration: none;
}

.markdown-content a:hover {
  text-decoration: underline;
}

.markdown-content table {
  border-collapse: collapse;
  margin: 1em 0;
  width: 100%;
}

.markdown-content th,
.markdown-content td {
  border: 1px solid var(--vscode-input-border);
  padding: 0.5em;
}

.markdown-content th {
  background-color: var(--vscode-input-background);
}

/* Settings Panel Styles */
.header-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.model-selector {
  position: relative;
  min-width: 180px;
}

.model-selector select {
  width: 100%;
  padding: 4px 8px;
  font-size: 0.8rem;
  border: 1px solid var(--vscode-input-border);
  border-radius: 4px;
  background-color: var(--vscode-input-background);
  color: var(--vscode-input-foreground);
  cursor: pointer;
  appearance: none;
  padding-right: 24px;
}

.model-selector select.loading {
  opacity: 0.7;
  cursor: not-allowed;
}

.model-selector::after {
  content: '▼';
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  font-size: 0.8rem;
  color: var(--vscode-input-foreground);
}

.model-progress {
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--vscode-progressBar-background);
  border-radius: 1px;
  overflow: hidden;
}

.model-progress .progress-bar {
  height: 100%;
  background-color: var(--vscode-progressBar-foreground);
  transition: width 0.3s ease;
}

.banner-content {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 16px;
}

.banner-message {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  color: var(--vscode-inputValidation-errorForeground);
  font-size: 0.8rem;
}

.warning-icon {
  font-size: 1.1rem;
}

.markdown-content {
  font-family: var(--vscode-font-family);
  line-height: 1.5;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  margin-top: 1em;
  margin-bottom: 0.5em;
  color: var(--vscode-foreground);
}

.markdown-content p {
  margin: 0.5em 0;
}

.markdown-content ul,
.markdown-content ol {
  margin: 0.5em 0;
  padding-left: 1.5em;
}

.markdown-content code {
  font-family: var(--vscode-editor-font-family);
  background-color: var(--vscode-textBlockQuote-background);
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-size: 0.8em;
}

.markdown-content pre {
  background-color: var(--vscode-textBlockQuote-background);
  padding: 1em;
  border-radius: 4px;
  overflow-x: auto;
}

.markdown-content blockquote {
  border-left: 4px solid var(--vscode-textBlockQuote-border);
  margin: 0.5em 0;
  padding-left: 1em;
  color: var(--vscode-textBlockQuote-foreground);
}

.markdown-content a {
  color: var(--vscode-textLink-foreground);
  text-decoration: none;
}

.markdown-content a:hover {
  text-decoration: underline;
}

.markdown-content table {
  border-collapse: collapse;
  margin: 1em 0;
  width: 100%;
}

.markdown-content th,
.markdown-content td {
  border: 1px solid var(--vscode-input-border);
  padding: 0.5em;
}

.markdown-content th {
  background-color: var(--vscode-input-background);
}

/* Settings Panel Styles */
.header-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.model-selector,
.model-selector-bottom {
  position: relative;
  min-width: 180px;
}

.model-selector select,
.model-selector-bottom select {
  width: 100%;
  padding: 4px 8px;
  font-size: 0.8rem;
  border: 1px solid var(--vscode-input-border);
  border-radius: 4px;
  background-color: var(--vscode-input-background);
  color: var(--vscode-input-foreground);
  cursor: pointer;
  appearance: none;
  padding-right: 24px;
}

.model-selector select.loading,
.model-selector-bottom select.loading {
  opacity: 0.7;
  cursor: not-allowed;
}

.model-selector::after {
  content: '▼';
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  font-size: 0.8rem;
  color: var(--vscode-input-foreground);
}

.model-selector-bottom::after {
  content: '▼';
  position: absolute;
  right: 6px;
  top: 50%;
  transform: translateY(-50%);
  pointer-events: none;
  font-size: 0.6rem;
  color: #cccccc;
}

/* Specific styles for the bottom model selector */
.model-selector-bottom {
  width: auto;
  min-width: 110px;
  max-width: 140px;
  margin-right: 2px;
}

.model-selector-bottom select {
  height: 24px;
  border-radius: 4px;
  background-color: var(--vscode-input-background);
  color: #cccccc;
  font-size: 0.7rem;
  padding-right: 18px;
  border: 1px solid var(--vscode-badge-background);
}

.model-selector-bottom::before {
  /* content: 'AI'; */
  position: absolute;
  left: 6px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 0.65rem;
  color: #cccccc;
  pointer-events: none;
  font-weight: 500;
}

.model-progress {
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: var(--vscode-progressBar-background);
  border-radius: 1px;
  overflow: hidden;
}

.model-progress .progress-bar {
  height: 100%;
  background-color: var(--vscode-progressBar-foreground);
  transition: width 0.3s ease;
}
