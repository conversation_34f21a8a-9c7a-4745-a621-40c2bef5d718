const esbuild = require('esbuild');
const path = require('path');
const fs = require('fs');

const isWatch = process.argv.includes('--watch');

// Function to get all files from a directory recursively
function getAllFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  files.forEach(file => {
    const filePath = path.join(dir, file);
    if (fs.statSync(filePath).isDirectory()) {
      fileList = getAllFiles(filePath, fileList);
    } else if (file.endsWith('.ts')) {
      fileList.push(filePath);
    }
  });
  return fileList;
}

// Get all worker files
const workerFiles = getAllFiles(path.join(__dirname, 'src/workers'));

/** @type {import('esbuild').BuildOptions} */
const baseConfig = {
  entryPoints: [
    'src/extension.ts',
    ...workerFiles
  ],
  bundle: true,
  platform: 'node',
  target: 'node18',
  outdir: 'dist',
  format: 'cjs',
  sourcemap: true,
  minify: process.env.NODE_ENV === 'production',
  external: ['vscode'],
  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development'),
    // Define global for transformers
    'global': 'globalThis'
  },
  // Handle native dependencies and .node files
  plugins: [
    {
      name: 'native-node-modules',
      setup(build) {
        // Handle .node files by marking them as external
        build.onResolve({ filter: /\.node$/ }, (args) => ({
          path: args.path,
          external: true,
        }));

        // Handle native modules by providing stubs instead of marking external
        build.onResolve({ filter: /^onnxruntime-node$/ }, () => ({
          path: 'onnxruntime-node-stub',
          namespace: 'stub'
        }));

        build.onResolve({ filter: /^sharp$/ }, () => ({
          path: 'sharp-stub',
          namespace: 'stub'
        }));

        // Provide stub implementations for native modules
        build.onLoad({ filter: /.*/, namespace: 'stub' }, (args) => {
          if (args.path === 'onnxruntime-node-stub') {
            return {
              contents: `
                // Comprehensive stub for onnxruntime-node
                // This ensures transformers falls back to WASM backend gracefully
                module.exports = {
                  InferenceSession: {
                    create: async function() {
                      // Return a rejected promise to trigger graceful fallback
                      throw new Error('ONNX Node backend not available, falling back to WASM');
                    }
                  },
                  Tensor: function() {
                    throw new Error('ONNX Node backend not available, falling back to WASM');
                  },
                  env: {
                    wasm: {},
                    webgl: {}
                  }
                };
              `,
              loader: 'js'
            };
          }
          if (args.path === 'sharp-stub') {
            return {
              contents: `
                // Stub for sharp
                module.exports = function() {
                  throw new Error('Sharp is not available in this environment');
                };
              `,
              loader: 'js'
            };
          }
        });

        // Force @xenova/transformers to be bundled but allow its native deps to be external
        build.onResolve({ filter: /^@xenova\/transformers$/ }, () => {
          return {
            path: require.resolve('@xenova/transformers'),
            external: false
          };
        });

        // Handle import.meta.url issues in transformers
        build.onLoad({ filter: /transformers.*\.js$/ }, async (args) => {
          const contents = await require('fs').promises.readFile(args.path, 'utf8');

          // Replace problematic patterns that cause URL errors
          let transformedContents = contents;

          // First, handle the most specific patterns
          transformedContents = transformedContents
            // Handle path.dirname(fileURLToPath(import.meta.url)) patterns first
            .replace(/path\.dirname\(fileURLToPath\(import\.meta\.url\)\)/g, '__dirname')
            .replace(/dirname\(fileURLToPath\(import\.meta\.url\)\)/g, '__dirname')

            // Handle fileURLToPath calls
            .replace(/fileURLToPath\(import\.meta\.url\)/g, '__dirname')
            .replace(/fileURLToPath\([^)]+\)/g, '__dirname')

            // Handle variable assignments that might cause undefined
            .replace(/const\s+(\w+)\s*=\s*fileURLToPath\([^)]+\)/g, 'const $1 = __dirname')
            .replace(/let\s+(\w+)\s*=\s*fileURLToPath\([^)]+\)/g, 'let $1 = __dirname')
            .replace(/var\s+(\w+)\s*=\s*fileURLToPath\([^)]+\)/g, 'var $1 = __dirname')

            // Handle path.dirname with variables that might be undefined
            .replace(/path\.dirname\((\w+)\)/g, '(__dirname || process.cwd())')
            .replace(/dirname\((\w+)\)/g, '(__dirname || process.cwd())')

            // Replace import.meta.url with a safe value
            .replace(/import\.meta\.url/g, 'require("url").pathToFileURL(__dirname + "/module.js").href')

            // Handle URL constructors
            .replace(/new URL\([^,]+,\s*import\.meta\.url\)/g, 'require("url").pathToFileURL(__dirname)')

            // Handle any remaining __dirname redefinitions
            .replace(/const\s+__dirname\s*=\s*[^;]+;/g, '// __dirname already available')
            .replace(/let\s+__dirname\s*=\s*[^;]+;/g, '// __dirname already available')
            .replace(/var\s+__dirname\s*=\s*[^;]+;/g, '// __dirname already available');

          return {
            contents: transformedContents,
            loader: 'js'
          };
        });
      }
    }
  ],
  // Add loader for .node files (though they should be external)
  loader: {
    '.node': 'file'
  },
  // Add banner to ensure globals are available
  banner: {
    js: `
// Ensure required globals are available for transformers
if (typeof __dirname === 'undefined') {
  global.__dirname = process.cwd();
}
if (typeof __filename === 'undefined') {
  global.__filename = __dirname + '/bundled.js';
}
// Polyfill for import.meta in bundled environment
if (typeof global.importMeta === 'undefined') {
  global.importMeta = {
    url: require('url').pathToFileURL(__dirname + '/module.js').href,
    resolve: (specifier) => require('url').pathToFileURL(__dirname + '/' + specifier).href
  };
}
    `.trim()
  },
};

if (isWatch) {
  esbuild.context(baseConfig).then(context => {
    context.watch();
  });
} else {
  esbuild.build(baseConfig).catch(() => process.exit(1));
}