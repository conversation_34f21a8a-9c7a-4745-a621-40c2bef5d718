const esbuild = require('esbuild');
const path = require('path');
const fs = require('fs');

const isWatch = process.argv.includes('--watch');

// Function to get all files from a directory recursively
function getAllFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  files.forEach(file => {
    const filePath = path.join(dir, file);
    if (fs.statSync(filePath).isDirectory()) {
      fileList = getAllFiles(filePath, fileList);
    } else if (file.endsWith('.ts')) {
      fileList.push(filePath);
    }
  });
  return fileList;
}

// Get all worker files
const workerFiles = getAllFiles(path.join(__dirname, 'src/workers'));

/** @type {import('esbuild').BuildOptions} */
const baseConfig = {
  entryPoints: [
    'src/extension.ts',
    ...workerFiles
  ],
  bundle: true,
  platform: 'node',
  target: 'node18',
  outdir: 'dist',
  format: 'cjs',
  sourcemap: true,
  minify: process.env.NODE_ENV === 'production',
  external: ['vscode'],
  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development')
  },
  // Handle native dependencies and .node files
  plugins: [
    {
      name: 'native-node-modules',
      setup(build) {
        // Handle .node files by marking them as external
        build.onResolve({ filter: /\.node$/ }, (args) => ({
          path: args.path,
          external: true,
        }));

        // Handle native modules that contain .node files
        build.onResolve({ filter: /^(onnxruntime-node|sharp)$/ }, (args) => ({
          path: args.path,
          external: true,
        }));

        // Force @xenova/transformers to be bundled but allow its native deps to be external
        build.onResolve({ filter: /^@xenova\/transformers$/ }, () => {
          return {
            path: require.resolve('@xenova/transformers'),
            external: false
          };
        });
      }
    }
  ],
  // Add loader for .node files (though they should be external)
  loader: {
    '.node': 'file'
  },
};

if (isWatch) {
  esbuild.context(baseConfig).then(context => {
    context.watch();
  });
} else {
  esbuild.build(baseConfig).catch(() => process.exit(1));
}