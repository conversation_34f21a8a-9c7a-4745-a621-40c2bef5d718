import * as path from 'path';

// Set up environment variables before importing transformers
process.env.TRANSFORMERS_CACHE = process.env.TRANSFORMERS_CACHE || path.join(__dirname, '.cache');

// Import transformers directly - this will be bundled by esbuild
const transformers = require('@xenova/transformers');

// Configure transformers to use WASM backend and avoid native dependencies
try {
  if (transformers.env) {
    // Force WASM backend
    transformers.env.backends.onnx = false;
    transformers.env.backends.onnx_web = true;
    transformers.env.backends.tfjs = false;

    // Set cache directory
    transformers.env.cacheDir = process.env.TRANSFORMERS_CACHE;

    // Disable local models to force remote loading
    transformers.env.allowLocalModels = false;
    transformers.env.allowRemoteModels = true;

    // Use file system for caching
    transformers.env.useBrowserCache = false;
    transformers.env.useFS = true;
  }
} catch (error) {
  console.warn('Could not configure transformers environment:', error);
}
let extractor: any;

export async function initializeEmbeddingModel(embeddingModel: string, embeddingDirPath:string, progressCallback: (status: any) => void) {
    try {
      // Use the statically imported transformers module
      const { pipeline } = transformers;
      // Add model options with local_files_only set to false to allow downloading if needed
      // and add a longer timeout to handle potential network issues
      extractor = await pipeline(
        'feature-extraction',
        embeddingModel,
        {
          local_files_only: false,
          revision: 'main',
          quantized: true,
          cache_dir: path.join(embeddingDirPath, '.cache', 'transformers'),
          progress_callback: (progress: any) => {
            if (progress.status === 'progress') {
              const progressPercent = Math.round(progress.progress);
              progressCallback({
                type: progress.status,
                progress: progressPercent,
                message: `Downloading model: ${progressPercent}%`,
              });
            }
          },
        }
      );
      return extractor;
    } catch (error) {
      throw new Error(`Failed to initialize model: ${(error as Error).message}`);
    }
  }
  