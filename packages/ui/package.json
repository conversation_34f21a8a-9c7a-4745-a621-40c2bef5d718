{"name": "@repo/ui", "version": "0.0.0", "private": true, "exports": {"./button": "./src/button.tsx", "./card": "./src/card.tsx", "./code": "./src/code.tsx"}, "scripts": {"lint": "eslint . --max-warnings 0", "generate:component": "turbo gen react-component", "check-types": "tsc --noEmit"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@turbo/gen": "^2.4.0", "@types/node": "^22.13.0", "@types/react": "19.0.8", "@types/react-dom": "19.0.3", "eslint": "^9.20.0", "typescript": "5.7.3"}, "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0"}}